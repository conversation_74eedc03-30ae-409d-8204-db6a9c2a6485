fileFormatVersion: 2
guid: 87032b7560bddab47bf4b9e08754edf8
timeCreated: **********
licenseType: Free
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: bone001
    100002: bone002
    100004: bone003
    100006: bone004
    100008: bone005
    100010: bone006
    100012: bone007
    100014: bone008
    100016: bone009
    100018: bone010
    100020: bone011
    100022: bone012
    100024: bone013
    100026: bone014
    100028: bone015
    100030: bone016
    100032: bone017
    100034: bone018
    100036: bone019
    100038: bone020
    100040: bone021
    100042: bone022
    100044: bone023
    100046: bone024
    100048: bone025
    100050: bone026
    100052: bone027
    100054: bone028
    100056: bone029
    100058: bone030
    100060: bone031
    100062: bone032
    100064: bone033
    100066: //RootNode
    100068: jiqiangbao_new_a_01
    400000: bone001
    400002: bone002
    400004: bone003
    400006: bone004
    400008: bone005
    400010: bone006
    400012: bone007
    400014: bone008
    400016: bone009
    400018: bone010
    400020: bone011
    400022: bone012
    400024: bone013
    400026: bone014
    400028: bone015
    400030: bone016
    400032: bone017
    400034: bone018
    400036: bone019
    400038: bone020
    400040: bone021
    400042: bone022
    400044: bone023
    400046: bone024
    400048: bone025
    400050: bone026
    400052: bone027
    400054: bone028
    400056: bone029
    400058: bone030
    400060: bone031
    400062: bone032
    400064: bone033
    400066: //RootNode
    400068: jiqiangbao_new_a_01
    4300000: jiqiangbao_new_a_01
    7400000: idle
    9500000: //RootNode
    11100000: //RootNode
    13700000: jiqiangbao_new_a_01
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: idle
      takeName: Take 001
      firstFrame: 0
      lastFrame: 60
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 1
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: bone033
        weight: 1
      - path: bone033/bone001
        weight: 1
      - path: bone033/bone002
        weight: 1
      - path: bone033/bone003
        weight: 1
      - path: bone033/bone004
        weight: 1
      - path: bone033/bone005
        weight: 1
      - path: bone033/bone006
        weight: 1
      - path: bone033/bone007
        weight: 1
      - path: bone033/bone008
        weight: 1
      - path: bone033/bone009
        weight: 1
      - path: bone033/bone010
        weight: 1
      - path: bone033/bone011
        weight: 1
      - path: bone033/bone012
        weight: 1
      - path: bone033/bone013
        weight: 1
      - path: bone033/bone014
        weight: 1
      - path: bone033/bone015
        weight: 1
      - path: bone033/bone016
        weight: 1
      - path: bone033/bone017
        weight: 1
      - path: bone033/bone018
        weight: 1
      - path: bone033/bone019
        weight: 1
      - path: bone033/bone020
        weight: 1
      - path: bone033/bone021
        weight: 1
      - path: bone033/bone022
        weight: 1
      - path: bone033/bone023
        weight: 1
      - path: bone033/bone024
        weight: 1
      - path: bone033/bone025
        weight: 1
      - path: bone033/bone026
        weight: 1
      - path: bone033/bone027
        weight: 1
      - path: bone033/bone028
        weight: 1
      - path: bone033/bone029
        weight: 1
      - path: bone033/bone030
        weight: 1
      - path: bone033/bone031
        weight: 1
      - path: bone033/bone032
        weight: 1
      - path: jiqiangbao_new_a_01
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 1
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
