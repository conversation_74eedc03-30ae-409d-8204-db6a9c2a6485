fileFormatVersion: 2
guid: f76b61cad4fd747448cb4329d21bd372
timeCreated: **********
licenseType: Free
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: bone001
    100002: bone002
    100004: bone003
    100006: bone004
    100008: bone005
    100010: bone006
    100012: bone007
    100014: bone008
    100016: bone009
    100018: bone010
    100020: bone011
    100022: bone012
    100024: bone013
    100026: bone014
    100028: bone015
    100030: bone016
    100032: bone017
    100034: bone018
    100036: bone019
    100038: bone020
    100040: bone021
    100042: bone022
    100044: bone023
    100046: bone024
    100048: bone025
    100050: bone026
    100052: bone027
    100054: bone028
    100056: bone029
    100058: bone030
    100060: bone031
    100062: bone032
    100064: bone033
    100066: bone034
    100068: bone035
    100070: bone036
    100072: bone037
    100074: bone038
    100076: bone039
    100078: bone040
    100080: bone041
    100082: bone042
    100084: bone043
    100086: bone044
    100088: bone045
    100090: bone046
    100092: bone047
    100094: bone048
    100096: bone049
    100098: jiqiangbao_d_01
    100100: //RootNode
    400000: bone001
    400002: bone002
    400004: bone003
    400006: bone004
    400008: bone005
    400010: bone006
    400012: bone007
    400014: bone008
    400016: bone009
    400018: bone010
    400020: bone011
    400022: bone012
    400024: bone013
    400026: bone014
    400028: bone015
    400030: bone016
    400032: bone017
    400034: bone018
    400036: bone019
    400038: bone020
    400040: bone021
    400042: bone022
    400044: bone023
    400046: bone024
    400048: bone025
    400050: bone026
    400052: bone027
    400054: bone028
    400056: bone029
    400058: bone030
    400060: bone031
    400062: bone032
    400064: bone033
    400066: bone034
    400068: bone035
    400070: bone036
    400072: bone037
    400074: bone038
    400076: bone039
    400078: bone040
    400080: bone041
    400082: bone042
    400084: bone043
    400086: bone044
    400088: bone045
    400090: bone046
    400092: bone047
    400094: bone048
    400096: bone049
    400098: jiqiangbao_d_01
    400100: //RootNode
    4300000: jiqiangbao_d_01
    7400000: idle
    9500000: //RootNode
    11100000: //RootNode
    13700000: jiqiangbao_d_01
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: idle
      takeName: Take 001
      firstFrame: 0
      lastFrame: 60
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 1
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: bone049
        weight: 1
      - path: bone049/bone001
        weight: 1
      - path: bone049/bone002
        weight: 1
      - path: bone049/bone003
        weight: 1
      - path: bone049/bone004
        weight: 1
      - path: bone049/bone005
        weight: 1
      - path: bone049/bone006
        weight: 1
      - path: bone049/bone007
        weight: 1
      - path: bone049/bone008
        weight: 1
      - path: bone049/bone009
        weight: 1
      - path: bone049/bone010
        weight: 1
      - path: bone049/bone011
        weight: 1
      - path: bone049/bone012
        weight: 1
      - path: bone049/bone013
        weight: 1
      - path: bone049/bone014
        weight: 1
      - path: bone049/bone015
        weight: 1
      - path: bone049/bone016
        weight: 1
      - path: bone049/bone017
        weight: 1
      - path: bone049/bone018
        weight: 1
      - path: bone049/bone019
        weight: 1
      - path: bone049/bone020
        weight: 1
      - path: bone049/bone021
        weight: 1
      - path: bone049/bone022
        weight: 1
      - path: bone049/bone023
        weight: 1
      - path: bone049/bone024
        weight: 1
      - path: bone049/bone025
        weight: 1
      - path: bone049/bone026
        weight: 1
      - path: bone049/bone027
        weight: 1
      - path: bone049/bone028
        weight: 1
      - path: bone049/bone029
        weight: 1
      - path: bone049/bone030
        weight: 1
      - path: bone049/bone031
        weight: 1
      - path: bone049/bone032
        weight: 1
      - path: bone049/bone033
        weight: 1
      - path: bone049/bone034
        weight: 1
      - path: bone049/bone035
        weight: 1
      - path: bone049/bone036
        weight: 1
      - path: bone049/bone037
        weight: 1
      - path: bone049/bone038
        weight: 1
      - path: bone049/bone039
        weight: 1
      - path: bone049/bone040
        weight: 1
      - path: bone049/bone041
        weight: 1
      - path: bone049/bone042
        weight: 1
      - path: bone049/bone043
        weight: 1
      - path: bone049/bone044
        weight: 1
      - path: bone049/bone045
        weight: 1
      - path: bone049/bone046
        weight: 1
      - path: bone049/bone047
        weight: 1
      - path: bone049/bone048
        weight: 1
      - path: jiqiangbao_d_01
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 1
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
