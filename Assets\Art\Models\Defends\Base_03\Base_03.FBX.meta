fileFormatVersion: 2
guid: f601923dc1f466748906f310b4b285b8
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: base_03
  - first:
      1: 100004
    second: bone001
  - first:
      1: 100006
    second: bone002
  - first:
      1: 100008
    second: bone003
  - first:
      1: 100010
    second: bone004
  - first:
      1: 100012
    second: bone005
  - first:
      1: 100014
    second: bone006
  - first:
      1: 100016
    second: bone007
  - first:
      1: 100018
    second: bone008
  - first:
      1: 100020
    second: bone009
  - first:
      1: 100022
    second: bone010
  - first:
      1: 100024
    second: bone011
  - first:
      1: 100026
    second: bone012
  - first:
      1: 100028
    second: bone013
  - first:
      1: 100030
    second: bone014
  - first:
      1: 100032
    second: bone015
  - first:
      1: 100034
    second: bone016
  - first:
      1: 100036
    second: bone017
  - first:
      1: 100038
    second: bone018
  - first:
      1: 100040
    second: bone019
  - first:
      1: 100042
    second: bone020
  - first:
      1: 100044
    second: bone021
  - first:
      1: 100046
    second: bone022
  - first:
      1: 100048
    second: bone023
  - first:
      1: 100050
    second: bone024
  - first:
      1: 100052
    second: bone025
  - first:
      1: 100054
    second: bone026
  - first:
      1: 100056
    second: bone027
  - first:
      1: 100058
    second: bone028
  - first:
      1: 100060
    second: bone029
  - first:
      1: 100062
    second: bone030
  - first:
      1: 100064
    second: bone031
  - first:
      1: 100066
    second: bone032
  - first:
      1: 100068
    second: bone033
  - first:
      1: 100070
    second: bone034
  - first:
      1: 100072
    second: bone035
  - first:
      1: 100074
    second: bone036
  - first:
      1: 100076
    second: bone037
  - first:
      1: 100078
    second: bone038
  - first:
      1: 100080
    second: bone039
  - first:
      1: 100082
    second: bone040
  - first:
      1: 100084
    second: bone041
  - first:
      1: 100086
    second: bone042
  - first:
      1: 100088
    second: bone043
  - first:
      1: 100090
    second: bone044
  - first:
      1: 100092
    second: bone045
  - first:
      1: 100094
    second: bone046
  - first:
      1: 100096
    second: bone047
  - first:
      1: 100098
    second: bone048
  - first:
      1: 100100
    second: bone049
  - first:
      1: 100102
    second: bone050
  - first:
      1: 100104
    second: bone051
  - first:
      1: 100106
    second: bone052
  - first:
      1: 100108
    second: bone053
  - first:
      1: 100110
    second: bone054
  - first:
      1: 100112
    second: bone055
  - first:
      1: 100114
    second: bone056
  - first:
      1: 100116
    second: bone057
  - first:
      1: 100118
    second: bone058
  - first:
      1: 100120
    second: bone059
  - first:
      1: 100122
    second: bone060
  - first:
      1: 100124
    second: bone061
  - first:
      1: 100126
    second: bone062
  - first:
      1: 100128
    second: bone063
  - first:
      1: 100130
    second: bone064
  - first:
      1: 100132
    second: bone065
  - first:
      1: 100134
    second: bone066
  - first:
      1: 100136
    second: bone067
  - first:
      1: 100138
    second: bone068
  - first:
      1: 100140
    second: Bone069
  - first:
      1: 100142
    second: Plane042
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: base_03
  - first:
      4: 400004
    second: bone001
  - first:
      4: 400006
    second: bone002
  - first:
      4: 400008
    second: bone003
  - first:
      4: 400010
    second: bone004
  - first:
      4: 400012
    second: bone005
  - first:
      4: 400014
    second: bone006
  - first:
      4: 400016
    second: bone007
  - first:
      4: 400018
    second: bone008
  - first:
      4: 400020
    second: bone009
  - first:
      4: 400022
    second: bone010
  - first:
      4: 400024
    second: bone011
  - first:
      4: 400026
    second: bone012
  - first:
      4: 400028
    second: bone013
  - first:
      4: 400030
    second: bone014
  - first:
      4: 400032
    second: bone015
  - first:
      4: 400034
    second: bone016
  - first:
      4: 400036
    second: bone017
  - first:
      4: 400038
    second: bone018
  - first:
      4: 400040
    second: bone019
  - first:
      4: 400042
    second: bone020
  - first:
      4: 400044
    second: bone021
  - first:
      4: 400046
    second: bone022
  - first:
      4: 400048
    second: bone023
  - first:
      4: 400050
    second: bone024
  - first:
      4: 400052
    second: bone025
  - first:
      4: 400054
    second: bone026
  - first:
      4: 400056
    second: bone027
  - first:
      4: 400058
    second: bone028
  - first:
      4: 400060
    second: bone029
  - first:
      4: 400062
    second: bone030
  - first:
      4: 400064
    second: bone031
  - first:
      4: 400066
    second: bone032
  - first:
      4: 400068
    second: bone033
  - first:
      4: 400070
    second: bone034
  - first:
      4: 400072
    second: bone035
  - first:
      4: 400074
    second: bone036
  - first:
      4: 400076
    second: bone037
  - first:
      4: 400078
    second: bone038
  - first:
      4: 400080
    second: bone039
  - first:
      4: 400082
    second: bone040
  - first:
      4: 400084
    second: bone041
  - first:
      4: 400086
    second: bone042
  - first:
      4: 400088
    second: bone043
  - first:
      4: 400090
    second: bone044
  - first:
      4: 400092
    second: bone045
  - first:
      4: 400094
    second: bone046
  - first:
      4: 400096
    second: bone047
  - first:
      4: 400098
    second: bone048
  - first:
      4: 400100
    second: bone049
  - first:
      4: 400102
    second: bone050
  - first:
      4: 400104
    second: bone051
  - first:
      4: 400106
    second: bone052
  - first:
      4: 400108
    second: bone053
  - first:
      4: 400110
    second: bone054
  - first:
      4: 400112
    second: bone055
  - first:
      4: 400114
    second: bone056
  - first:
      4: 400116
    second: bone057
  - first:
      4: 400118
    second: bone058
  - first:
      4: 400120
    second: bone059
  - first:
      4: 400122
    second: bone060
  - first:
      4: 400124
    second: bone061
  - first:
      4: 400126
    second: bone062
  - first:
      4: 400128
    second: bone063
  - first:
      4: 400130
    second: bone064
  - first:
      4: 400132
    second: bone065
  - first:
      4: 400134
    second: bone066
  - first:
      4: 400136
    second: bone067
  - first:
      4: 400138
    second: bone068
  - first:
      4: 400140
    second: Bone069
  - first:
      4: 400142
    second: Plane042
  - first:
      23: 2300000
    second: Plane042
  - first:
      33: 3300000
    second: Plane042
  - first:
      43: 4300000
    second: base_03
  - first:
      43: 4300002
    second: Plane042
  - first:
      95: 9500000
    second: //RootNode
  - first:
      111: 11100000
    second: //RootNode
  - first:
      137: 13700000
    second: base_03
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - e2795a1c8cc9f1742a83d8c4ac0a830e
  - 7218ae6ff654ae0429d2afa4b3f87cb4
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 1
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
