fileFormatVersion: 2
guid: eb0e608275b8b354e8f22bad0db1f50b
timeCreated: **********
licenseType: Free
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: bone001
    100002: bone002
    100004: bone003
    100006: bone004
    100008: bone005
    100010: bone006
    100012: bone007
    100014: bone008
    100016: bone009
    100018: bone010
    100020: bone011
    100022: bone012
    100024: bone013
    100026: bone014
    100028: bone015
    100030: bone016
    100032: bone017
    100034: bone018
    100036: bone019
    100038: bone020
    100040: bone021
    100042: bone022
    100044: bone023
    100046: bone024
    100048: bone025
    100050: bone026
    100052: bone027
    100054: bone028
    100056: bone029
    100058: bone030
    100060: bone031
    100062: bone032
    100064: bone033
    100066: bone034
    100068: bone035
    100070: bone036
    100072: bone037
    100074: D_WP
    100076: Fortress_10
    100078: //RootNode
    100080: Plane057
    400000: bone001
    400002: bone002
    400004: bone003
    400006: bone004
    400008: bone005
    400010: bone006
    400012: bone007
    400014: bone008
    400016: bone009
    400018: bone010
    400020: bone011
    400022: bone012
    400024: bone013
    400026: bone014
    400028: bone015
    400030: bone016
    400032: bone017
    400034: bone018
    400036: bone019
    400038: bone020
    400040: bone021
    400042: bone022
    400044: bone023
    400046: bone024
    400048: bone025
    400050: bone026
    400052: bone027
    400054: bone028
    400056: bone029
    400058: bone030
    400060: bone031
    400062: bone032
    400064: bone033
    400066: bone034
    400068: bone035
    400070: bone036
    400072: bone037
    400074: D_WP
    400076: Fortress_10
    400078: //RootNode
    400080: Plane057
    4300000: Fortress_10
    4300002: Plane057
    7400000: Take 001
    9500000: //RootNode
    11100000: //RootNode
    13700000: Fortress_10
    13700002: Plane057
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 1
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
