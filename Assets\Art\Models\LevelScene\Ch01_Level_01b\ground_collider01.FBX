; FBX 6.1.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 6100
	CreationTimeStamp:  {
		Version: 1000
		Year: 2017
		Month: 11
		Day: 6
		Hour: 14
		Minute: 51
		Second: 37
		Millisecond: 294
	}
	Creator: "FBX SDK/FBX Plugins version 2014.0.1"
}

; Document Description
;------------------------------------------------------------------

Document:  {
	Name: ""
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 8
	ObjectType: "Model" {
		Count: 1
	}
	ObjectType: "Material" {
		Count: 1
	}
	ObjectType: "SceneInfo" {
		Count: 1
	}
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "CollectionExclusive" {
		Count: 4
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: "Model::Plane069", "Mesh" {
		Version: 232
		Properties60:  {
			Property: "QuaternionInterpolate", "enum", "",0
			Property: "RotationOffset", "Vector3D", "",0,0,0
			Property: "RotationPivot", "Vector3D", "",0,0,0
			Property: "ScalingOffset", "Vector3D", "",0,0,0
			Property: "ScalingPivot", "Vector3D", "",0,0,0
			Property: "TranslationActive", "bool", "",0
			Property: "TranslationMin", "Vector3D", "",0,0,0
			Property: "TranslationMax", "Vector3D", "",0,0,0
			Property: "TranslationMinX", "bool", "",0
			Property: "TranslationMinY", "bool", "",0
			Property: "TranslationMinZ", "bool", "",0
			Property: "TranslationMaxX", "bool", "",0
			Property: "TranslationMaxY", "bool", "",0
			Property: "TranslationMaxZ", "bool", "",0
			Property: "RotationOrder", "enum", "",0
			Property: "RotationSpaceForLimitOnly", "bool", "",0
			Property: "RotationStiffnessX", "double", "",0
			Property: "RotationStiffnessY", "double", "",0
			Property: "RotationStiffnessZ", "double", "",0
			Property: "AxisLen", "double", "",10
			Property: "PreRotation", "Vector3D", "",0,0,0
			Property: "PostRotation", "Vector3D", "",0,0,0
			Property: "RotationActive", "bool", "",0
			Property: "RotationMin", "Vector3D", "",0,0,0
			Property: "RotationMax", "Vector3D", "",0,0,0
			Property: "RotationMinX", "bool", "",0
			Property: "RotationMinY", "bool", "",0
			Property: "RotationMinZ", "bool", "",0
			Property: "RotationMaxX", "bool", "",0
			Property: "RotationMaxY", "bool", "",0
			Property: "RotationMaxZ", "bool", "",0
			Property: "InheritType", "enum", "",1
			Property: "ScalingActive", "bool", "",0
			Property: "ScalingMin", "Vector3D", "",0,0,0
			Property: "ScalingMax", "Vector3D", "",0,0,0
			Property: "ScalingMinX", "bool", "",0
			Property: "ScalingMinY", "bool", "",0
			Property: "ScalingMinZ", "bool", "",0
			Property: "ScalingMaxX", "bool", "",0
			Property: "ScalingMaxY", "bool", "",0
			Property: "ScalingMaxZ", "bool", "",0
			Property: "GeometricTranslation", "Vector3D", "",0,0,0
			Property: "GeometricRotation", "Vector3D", "",0,0,0
			Property: "GeometricScaling", "Vector3D", "",1,1,1
			Property: "MinDampRangeX", "double", "",0
			Property: "MinDampRangeY", "double", "",0
			Property: "MinDampRangeZ", "double", "",0
			Property: "MaxDampRangeX", "double", "",0
			Property: "MaxDampRangeY", "double", "",0
			Property: "MaxDampRangeZ", "double", "",0
			Property: "MinDampStrengthX", "double", "",0
			Property: "MinDampStrengthY", "double", "",0
			Property: "MinDampStrengthZ", "double", "",0
			Property: "MaxDampStrengthX", "double", "",0
			Property: "MaxDampStrengthY", "double", "",0
			Property: "MaxDampStrengthZ", "double", "",0
			Property: "PreferedAngleX", "double", "",0
			Property: "PreferedAngleY", "double", "",0
			Property: "PreferedAngleZ", "double", "",0
			Property: "LookAtProperty", "object", ""
			Property: "UpVectorProperty", "object", ""
			Property: "Show", "bool", "",1
			Property: "NegativePercentShapeSupport", "bool", "",1
			Property: "DefaultAttributeIndex", "int", "",0
			Property: "Freeze", "bool", "",0
			Property: "LODBox", "bool", "",0
			Property: "Lcl Translation", "Lcl Translation", "A",927.128662109375,-22390.0234375,0
			Property: "Lcl Rotation", "Lcl Rotation", "A",0,0,0
			Property: "Lcl Scaling", "Lcl Scaling", "A",1,1,1
			Property: "Visibility", "Visibility", "A",1
			Property: "MaxHandle", "int", "UH",753
			Property: "Color", "ColorRGB", "N",0,0,0
			Property: "BBoxMin", "Vector3D", "N",0,0,0
			Property: "BBoxMax", "Vector3D", "N",0,0,0
			Property: "Primary Visibility", "bool", "N",1
			Property: "Casts Shadows", "bool", "N",1
			Property: "Receive Shadows", "bool", "N",1
		}
		MultiLayer: 0
		MultiTake: 0
		Shading: T
		Culling: "CullingOff"
		Vertices: -3093.86083984375,-1187.32141113281,0,4215.3017578125,-1187.32141113281,0,-3330.5615234375,756.647583007813,0,4215.3017578125
        ,598.171020507813,0,-1178.72180175781,756.647583007813,0,-355.10498046875,738.163208007813,0,287.48486328125,865.946411132813
        ,0,549.602783203125,-1187.32141113281,0,503.418487548828,792.442138671875,0,910.177917480469,798.501892089844,0,-877.36328125
        ,-1187.32141113281,0,-851.224609375,681.538208007813,0,-621.915893554688,720.448364257813,0,-421.938385009766,-1187.32141113281
        ,0,-33.81005859375,818.169067382813,0,109.714111328125,868.999145507813,0,1219.72680664063,756.647583007813,0,1409.00366210938
        ,-1187.32141113281,0,-1127.65698242188,-1187.32141113281,0,-1306.26245117188,756.647583007813,0,-1356.76416015625
        ,649.076599121094,0,-1564.63098144531,636.748046875,0,-2104.77954101563,756.647583007813,0,-1813.68090820313,668.987609863281
        ,0,2079.09765625,756.647583007813,0,1794.34008789063,-1187.32141113281,0,2643.94287109375,736.124145507813,0,2193.90991210938
        ,-1187.32141113281,0,3187.5986328125,598.171020507813,0,3188.06103515625,-1187.32141113281,0,-1862.80871582031,-1187.32141113281
        ,0,-1853.41857910156,-543.425354003906,0,-1966.91052246094,-562.935546875,0,-2045.71899414063,-645.89453125,0,-2067.9228515625
        ,-718.734375,0,-2060.67895507813,-776.169921875,0,-2039.11181640625,-828.083923339844,0,-1863.3671875,-922.960510253906
        ,0,-1707.2568359375,-866.12890625,0,-1664.75634765625,-821.458984375,0,-1644.44738769531,-777.406860351563,0,-1645.16052246094
        ,-703.617431640625,0,-1713.75915527344,-587.94140625,0,-1763.72900390625,-562.687438964844,0,-2561.71459960938,-294.375823974609
        ,0,-1276.48291015625,-624.821105957031,0,-1927.47412109375,-910.666015625,0,-1803.8037109375,-911.766967773438,0,-1912.77465820313
        ,-547.912231445313,0,-1657.64636230469,-659.728393554688,0,-1682.56176757813,-623.603332519531,0,-1811.36535644531
        ,-548.20751953125,0,-1751.93017578125,-896.163208007813,0,-1985.63586425781,-887.123657226563,0,-2011.61767578125
        ,-862.327392578125,0,-2011.41125488281,-600.6982421875,0,-900.568237304688,333.978637695313,0,-1318.04455566406,369.789733886719
        ,0,-980.72705078125,693.312927246094,0,23.9097499847412,-1187.32153320313,0,-154.442153930664,778.166137695313,0,718.597045898438
        ,758.177612304688,0,1046.3330078125,793.594665527344,0,611.007751464844,694.343078613281,0,-107.607223510742,492.140045166016
        ,0,-279.848876953125,452.916717529297,0,-331.133911132813,401.533233642578,0,-377.120941162109,297.876922607422,0
        ,-345.321716308594,128.2578125,0,-246.598937988281,53.10205078125,0,-145.125366210938,24.5476531982422,0,8.23916625976563
        ,45.9081268310547,0,89.43310546875,109.460357666016,0,136.672119140625,187.443344116211,0,144.077575683594,309.580078125
        ,0,-9.36843109130859,472.574829101563,0,89.4506530761719,397.164581298828,0,-380.90771484375,199.02522277832,0,-205.558364868164
        ,486.049041748047,0,-69.3942184448242,26.6403198242188,0,1316.02416992188,-548.75048828125,0,1198.82250976563,-581.37890625
        ,0,1103.888671875,-652.4609375,0,1073.22961425781,-747.587890625,0,1100.03845214844,-840.498046875,0,1181.81079101563
        ,-909.615234375,0,1292.25366210938,-953.66015625,0,1386.71264648438,-958.38134765625,0,1498.35668945313,-932.564453125
        ,0,1577.79833984375,-864.130859375,0,1614.49169921875,-785.501953125,0,1600.41918945313,-694.64453125,0,1531.8896484375
        ,-616.072265625,0,1429.326171875,-557.98046875,0,599.784362792969,-193.91764831543,0,2093.63403320313,-719.075866699219
        ,0,2595.26635742188,-734.52880859375,0,2199.51245117188,1029.20422363281,0,2575.53881835938,1029.20422363281,0,2151.58227539063
        ,839.082153320313,0,2571.4580078125,839.082153320313,0,2169.50268554688,1143.36633300781,0,2605.54858398438,1143.36633300781
        ,0,1802.828125,-1398.21594238281,0,2185.42163085938,-1398.21594238281,0,1802.828125,-1262.84484863281,0,2185.42163085938
        ,-1262.84484863281,0
		PolygonVertexIndex: 8,6,-95,4,19,-21,25,27,-96,28,26,-97,96,29,-29,1,3,-29,28,29,-2,20,21,-58,22,2,-45,0,54,-37,21,51,-44,48,23,-45,44
        ,23,-23,55,32,-45,44,34,-34,2,0,-45,35,34,-45,44,0,-36,36,35,-1,39,38,-19,47,37,-31,41,40,-46,39,18,-46,45,40,-40
        ,10,56,-46,45,18,-11,49,41,-46,43,42,-22,30,37,-47,30,18,-53,52,47,-31,44,32,-49,48,31,-24,45,50,-50,42,50,-46,45
        ,21,-43,21,23,-32,31,51,-22,18,38,-53,30,46,-54,30,53,-55,54,0,-31,44,33,-56,57,56,-5,4,20,-58,56,58,-5,21,45,-58
        ,56,57,-46,56,10,-14,12,11,-57,56,11,-59,69,59,-71,71,59,-73,6,76,-75,74,94,-7,15,14,-65,64,75,-16,59,69,-69,68,13
        ,-60,74,73,-95,73,7,-95,63,8,-95,79,70,-60,6,15,-76,75,76,-7,72,59,-8,7,73,-73,67,66,-57,56,77,-68,64,14,-61,60,78
        ,-65,65,78,-61,60,5,-66,59,71,-80,84,7,-86,24,16,-81,80,93,-25,83,82,-95,82,81,-95,94,81,-63,62,9,-95,94,9,-62,94
        ,61,-64,84,83,-95,94,7,-85,7,86,-86,7,17,-88,87,86,-8,91,95,-25,24,92,-92,93,92,-25,91,90,-96,90,89,-96,26,24,-96
        ,95,96,-27,89,25,-96,88,87,-18,17,25,-89,89,88,-26,81,80,-17,16,62,-82,95,27,-97,96,27,-30,66,65,-57,5,12,-57,56,65
        ,-6,56,13,-69,68,77,-57,26,100,-100,99,24,-27,98,97,-100,99,100,-99,97,98,-103,102,101,-98,25,105,-107,106,27,-26
        ,103,104,-107,106,105,-104
		Edges: 0,1,2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,19,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,37,39,40,41,42,43,44,45,46
        ,48,50,52,54,57,58,59,60,61,62,63,64,65,67,68,70,72,73,74,76,78,80,81,82,85,86,87,88,89,90,94,96,97,99,100,102,104
        ,105,108,110,111,115,118,119,121,122,124,127,129,130,131,135,136,139,145,146,147,148,149,151,153,154,155,156,157,158
        ,159,160,161,162,165,166,167,168,169,172,173,174,175,177,178,180,181,183,185,186,188,189,191,192,196,197,199,201,202
        ,203,204,205,208,209,210,211,213,215,216,217,220,222,223,224,225,226,227,228,229,231,232,233,234,235,238,239,240,241
        ,244,245,247,249,251,255,256,258,259,260,261,264,265,266,267,268,270,273,274,276,277,279,281,282,285,288,290,291,292
        ,294,297,299,300,304,307,309,310,312,314,320,321,324,325,326,327,330,331,332,334,337,338,339,340,342,343,344,345,348
        ,349,350,352
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: 0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
             ,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0
             ,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
             ,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
             ,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,0.999999940395355
             ,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1
             ,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0
             ,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
             ,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
             ,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,0.999999940395355
             ,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,0.999999940395355,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355
             ,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,0.999999940395355,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1
             ,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355
             ,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1
             ,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355
             ,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
             ,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1
             ,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,0.999999940395355,0,0,0.999999940395355
             ,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355
             ,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
             ,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355
             ,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,0.999999940395355,0,0,0.999999940395355,0,0,0.999999940395355
             ,0,0,1,0,0,0.999999940395355,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1
             ,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355
             ,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355
             ,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0
             ,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0
             ,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVChannel_1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: 0,0,1,0,0,1,1,1,0.5,1,0.75,1,0.875,1,0.924223065376282,0,0.917031705379486,1,0.962965607643127,1,0.625,0,0.625
             ,1,0.6875,1,0.71875,0,0.8125,1,0.84375,1,0.984375,1,0.984375,0,0.375,0,0.25,1,0.25,0.93130362033844,0.206946164369583
             ,0.927654325962067,0.151383504271507,1,0.186015874147415,0.954906702041626,0.9921875,1,0.9921875,0,0.99609375
             ,1,0.99609375,0,0.998046875,1,0.998046875,0,0.184528082609177,0,0.185068249702454,0.34669303894043,0.169267594814301
             ,0.331807017326355,0.154178202152252,0.294056057929993,0.148617118597031,0.240126237273216,0.152100205421448
             ,0.184849113225937,0.166115909814835,0.141704887151718,0.184698402881622,0.109319254755974,0.194459468126297
             ,0.129430845379829,0.202703356742859,0.161420658230782,0.206471681594849,0.208072364330292,0.208125814795494
             ,0.262722045183182,0.200345918536186,0.313687980175018,0.191887378692627,0.337682783603668,0.0791474729776382
             ,0.459341466426849,0.243412032723427,0.285662472248077,0.173885494470596,0.128164038062096,0.190223425626755
             ,0.12070294469595,0.175969049334526,0.338120579719543,0.204911261796951,0.283780455589294,0.202862709760666,0.297200441360474
             ,0.188665717840195,0.341939628124237,0.192346587777138,0.125077486038208,0.16962006688118,0.135597854852676,0.167754784226418
             ,0.138848662376404,0.161469250917435,0.31229692697525,0.6105597615242,0.772154092788696,0.374118626117706,0.765217304229736
             ,0.585344076156616,1,0.8125,0,0.78125,1,0.946789741516113,1,0.973135054111481,1,0.931910753250122,1,0.8125,0.748539090156555
             ,0.78125,0.744737029075623,0.767483294010162,0.708525657653809,0.760523796081543,0.656842172145844,0.765874147415161
             ,0.597746133804321,0.78125,0.556285738945007,0.8125,0.522923767566681,0.831072330474854,0.543497204780579,0.84375
             ,0.56228244304657,0.856683671474457,0.611014842987061,0.857635915279388,0.668826282024384,0.84375,0.700979828834534
             ,0.848989009857178,0.688848495483398,0.763383984565735,0.625250339508057,0.794894814491272,0.746397137641907
             ,0.82215827703476,0.533622741699219,0.984375,0.314844250679016,0.978010892868042,0.305834829807281,0.974328815937042
             ,0.279373347759247,0.970747590065002,0.223803073167801,0.971172571182251,0.165587425231934,0.974491655826569
             ,0.142109856009483,0.980160772800446,0.119193479418755,0.984375,0.117769412696362,0.986035048961639,0.139073520898819
             ,0.987163305282593,0.176823481917381,0.987690448760986,0.217271104454994,0.987632691860199,0.263110965490341
             ,0.986446619033813,0.295468658208847,0.98507833480835,0.3116475045681,0.924546241760254,0.491023451089859,0.9921875
             ,0.240870893001556,0.996195435523987,0.232921719551086,0.99609375,1,0.9921875,1,0.9921875,1,0.99609375,1,0.99609375
             ,1,0.9921875,1,0.9921875,0,0.99609375,0,0.9921875,0,0.99609375,0
			UVIndex: 8,6,94,4,19,20,25,27,95,28,26,96,96,29,28,1,3,28,28,29,1,20,21,57,22,2,44,0,54,36,21,51,43,48,23,44,44,23,22
             ,55,32,44,44,34,33,2,0,44,35,34,44,44,0,35,36,35,0,39,38,18,47,37,30,41,40,45,39,18,45,45,40,39,10,56,45,45,18
             ,10,49,41,45,43,42,21,30,37,46,30,18,52,52,47,30,44,32,48,48,31,23,45,50,49,42,50,45,45,21,42,21,23,31,31,51
             ,21,18,38,52,30,46,53,30,53,54,54,0,30,44,33,55,57,56,4,4,20,57,56,58,4,21,45,57,56,57,45,56,10,13,12,11,56,56
             ,11,58,69,59,70,71,59,72,6,76,74,74,94,6,15,14,64,64,75,15,59,69,68,68,13,59,74,73,94,73,7,94,63,8,94,79,70,59
             ,6,15,75,75,76,6,72,59,7,7,73,72,67,66,56,56,77,67,64,14,60,60,78,64,65,78,60,60,5,65,59,71,79,84,7,85,24,16
             ,80,80,93,24,83,82,94,82,81,94,94,81,62,62,9,94,94,9,61,94,61,63,84,83,94,94,7,84,7,86,85,7,17,87,87,86,7,91
             ,95,24,24,92,91,93,92,24,91,90,95,90,89,95,26,24,95,95,96,26,89,25,95,88,87,17,17,25,88,89,88,25,81,80,16,16
             ,62,81,95,27,96,96,27,29,66,65,56,5,12,56,56,65,5,56,13,68,68,77,56,26,100,99,99,24,26,97,98,99,99,100,97,98
             ,97,101,101,102,98,25,105,106,106,27,25,103,104,106,106,105,103
		}
		LayerElementVisibility: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByEdge"
			ReferenceInformationType: "Direct"
			Visibility: 1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1
             ,1,0,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
             ,1,0,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1
             ,0,1,1,1,1,1,1,0,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,0,1,1,1,1,1,1,0,0,1,1,1,0,1,1,1,0,1,1,0
             ,1,1,1,1,0,1,1,1,0,1
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: 0
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementVisibility"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
		NodeAttributeName: "Geometry::Plane069_ncl1_1"
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties60:  {
			Property: "DocumentUrl", "KString", "", "D:\wgame\trunk\project\client\wgame\Assets\Art\Models\LevelScene\Ch01_Level_01b\ground_collider01.FBX"
             
			Property: "SrcDocumentUrl", "KString", "", "D:\wgame\trunk\project\client\wgame\Assets\Art\Models\LevelScene\Ch01_Level_01b\ground_collider01.FBX"
             
			Property: "Original", "Compound", ""
			Property: "Original|ApplicationVendor", "KString", "", "Autodesk"
			Property: "Original|ApplicationName", "KString", "", "3ds Max"
			Property: "Original|ApplicationVersion", "KString", "", "2014"
			Property: "Original|DateTime_GMT", "DateTime", "", "06/11/2017 06:51:37.293"
			Property: "Original|FileName", "KString", "", "D:\wgame\trunk\project\client\wgame\Assets\Art\Models\LevelScene\Ch01_Level_01b\ground_collider01.FBX"
             
			Property: "LastSaved", "Compound", ""
			Property: "LastSaved|ApplicationVendor", "KString", "", "Autodesk"
			Property: "LastSaved|ApplicationName", "KString", "", "3ds Max"
			Property: "LastSaved|ApplicationVersion", "KString", "", "2014"
			Property: "LastSaved|DateTime_GMT", "DateTime", "", "06/11/2017 06:51:37.293"
		}
	}
	Material: "Material::13 - Default", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties60:  {
			Property: "ShadingModel", "KString", "", "phong"
			Property: "MultiLayer", "bool", "",0
			Property: "EmissiveColor", "Color", "A",0,0,0
			Property: "EmissiveFactor", "Number", "A",0
			Property: "AmbientColor", "Color", "A",0.34901961684227,0.34901961684227,0.34901961684227
			Property: "AmbientFactor", "Number", "A",1
			Property: "DiffuseColor", "Color", "A",0.34901961684227,0.34901961684227,0.34901961684227
			Property: "DiffuseFactor", "Number", "A",1
			Property: "Bump", "Vector3D", "",0,0,0
			Property: "NormalMap", "Vector3D", "",0,0,0
			Property: "BumpFactor", "double", "",1
			Property: "TransparentColor", "Color", "A",1,1,1
			Property: "TransparencyFactor", "Number", "A",0
			Property: "DisplacementColor", "ColorRGB", "",0,0,0
			Property: "DisplacementFactor", "double", "",1
			Property: "VectorDisplacementColor", "ColorRGB", "",0,0,0
			Property: "VectorDisplacementFactor", "double", "",1
			Property: "SpecularColor", "Color", "A",0.899999976158142,0.899999976158142,0.899999976158142
			Property: "SpecularFactor", "Number", "A",0
			Property: "ShininessExponent", "Number", "A",1.99999988079071
			Property: "ReflectionColor", "Color", "A",0,0,0
			Property: "ReflectionFactor", "Number", "A",1
			Property: "Emissive", "Vector3D", "",0,0,0
			Property: "Ambient", "Vector3D", "",0.34901961684227,0.34901961684227,0.34901961684227
			Property: "Diffuse", "Vector3D", "",0.34901961684227,0.34901961684227,0.34901961684227
			Property: "Specular", "Vector3D", "",0,0,0
			Property: "Shininess", "double", "",1.99999988079071
			Property: "Opacity", "double", "",1
			Property: "Reflectivity", "double", "",0
		}
	}
	GlobalSettings:  {
		Version: 1000
		Properties60:  {
			Property: "UpAxis", "int", "",2
			Property: "UpAxisSign", "int", "",1
			Property: "FrontAxis", "int", "",1
			Property: "FrontAxisSign", "int", "",-1
			Property: "CoordAxis", "int", "",0
			Property: "CoordAxisSign", "int", "",1
			Property: "OriginalUpAxis", "int", "",2
			Property: "OriginalUpAxisSign", "int", "",1
			Property: "UnitScaleFactor", "double", "",1
			Property: "OriginalUnitScaleFactor", "double", "",1
			Property: "AmbientColor", "ColorRGB", "",0,0,0
			Property: "DefaultCamera", "KString", "", "Producer Top"
			Property: "TimeMode", "enum", "",6
			Property: "TimeProtocol", "enum", "",2
			Property: "SnapOnFrameMode", "enum", "",0
			Property: "TimeSpanStart", "KTime", "",0
			Property: "TimeSpanStop", "KTime", "",153953860000
			Property: "CustomFrameRate", "double", "",-1
		}
	}
	CollectionExclusive: "DisplayLayer::Layer01", "DisplayLayer" {
		Properties60:  {
			Property: "Color", "ColorRGB", "",0.83921568627451,0.898039215686275,0.650980392156863
			Property: "Show", "bool", "",1
			Property: "Freeze", "bool", "",0
			Property: "LODBox", "bool", "",0
		}
	}
	CollectionExclusive: "DisplayLayer::uvok", "DisplayLayer" {
		Properties60:  {
			Property: "Color", "ColorRGB", "",0.105882352941176,0.694117647058824,0.105882352941176
			Property: "Show", "bool", "",1
			Property: "Freeze", "bool", "",0
			Property: "LODBox", "bool", "",0
		}
	}
	CollectionExclusive: "DisplayLayer::mg", "DisplayLayer" {
		Properties60:  {
			Property: "Color", "ColorRGB", "",0.83921568627451,0.898039215686275,0.650980392156863
			Property: "Show", "bool", "",1
			Property: "Freeze", "bool", "",0
			Property: "LODBox", "bool", "",0
		}
	}
	CollectionExclusive: "DisplayLayer::uv", "DisplayLayer" {
		Properties60:  {
			Property: "Color", "ColorRGB", "",0.443137254901961,0.529411764705882,0.0235294117647059
			Property: "Show", "bool", "",1
			Property: "Freeze", "bool", "",0
			Property: "LODBox", "bool", "",0
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	Connect: "OO", "Model::Plane069", "Model::Scene"
	Connect: "OO", "Material::13 - Default", "Model::Plane069"
}
;Version 5 settings
;------------------------------------------------------------------

Version5:  {
	AmbientRenderSettings:  {
		Version: 101
		AmbientLightColor: 0,0,0,1
	}
	FogOptions:  {
		FlogEnable: 0
		FogMode: 0
		FogDensity: 0.002
		FogStart: 0.3
		FogEnd: 1000
		FogColor: 1,1,1,1
	}
	Settings:  {
		FrameRate: "30"
		TimeFormat: 1
		SnapOnFrames: 0
		ReferenceTimeIndex: -1
		TimeLineStartTime: 0
		TimeLineStopTime: 153953860000
	}
	RendererSetting:  {
		DefaultCamera: "Producer Top"
		DefaultViewingMode: 0
	}
}
