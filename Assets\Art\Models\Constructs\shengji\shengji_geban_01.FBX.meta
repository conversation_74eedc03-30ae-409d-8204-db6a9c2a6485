fileFormatVersion: 2
guid: 0a1109843303bbd4489392251d8eeac5
timeCreated: **********
licenseType: Free
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Bone001
    100002: Bone002
    100004: Bone003
    100006: Bone004
    100008: Bone005
    100010: Bone011
    100012: Bone012
    100014: Bone013
    100016: Bone014
    100018: Bone015
    100020: Bone016
    100022: Bone017
    100024: Bone018
    100026: Dummy001
    100028: Object024
    100030: Particle View 001
    100032: Particle View 001 1
    100034: Particle View 001 2
    100036: Particle View 001 3
    100038: Particle View 001 4
    100040: Particle View 001 5
    100042: Particle View 001 6
    100044: Particle View 001 7
    100046: //RootNode
    100048: weixiujianzhu_01
    400000: Bone001
    400002: Bone002
    400004: Bone003
    400006: Bone004
    400008: Bone005
    400010: Bone011
    400012: Bone012
    400014: Bone013
    400016: Bone014
    400018: Bone015
    400020: Bone01<PERSON>
    400022: Bone017
    400024: Bone018
    400026: Dummy001
    400028: Object024
    400030: Particle View 001
    400032: Particle View 001 1
    400034: Particle View 001 2
    400036: Particle View 001 3
    400038: Particle View 001 4
    400040: Particle View 001 5
    400042: Particle View 001 6
    400044: Particle View 001 7
    400046: //RootNode
    400048: weixiujianzhu_01
    4300000: weixiujianzhu_01
    7400000: Take 001
    9500000: //RootNode
    11100000: //RootNode
    13700000: weixiujianzhu_01
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
