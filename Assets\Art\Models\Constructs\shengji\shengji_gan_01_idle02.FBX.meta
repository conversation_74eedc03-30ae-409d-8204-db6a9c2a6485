fileFormatVersion: 2
guid: 9fd1cb2c952e51b4cb0505eef19327d8
timeCreated: **********
licenseType: Free
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Bone006
    100002: Bone007
    100004: Bone008
    100006: Bone009
    100008: Bone010
    100010: Camera01
    100012: Camera01 1
    100014: Camera01.Target
    100016: Camera01.Target 1
    100018: Dummy001
    100020: Particle View 001
    100022: Particle View 001 1
    100024: Particle View 001 2
    100026: Particle View 001 3
    100028: Particle View 001 4
    100030: Particle View 001 5
    100032: Particle View 001 6
    100034: Particle View 001 7
    100036: //RootNode
    100038: weixiujianzhu_01
    100040: Bone019
    100042: Bone020
    100044: Bone021
    100046: Bone022
    100048: weixiujianzhu_01 1
    100050: Point001
    400000: Bone006
    400002: Bone007
    400004: Bone008
    400006: Bone009
    400008: Bone010
    400010: Camera01
    400012: Camera01 1
    400014: Camera01.Target
    400016: Camera01.Target 1
    400018: Dummy001
    400020: Particle View 001
    400022: Particle View 001 1
    400024: Particle View 001 2
    400026: Particle View 001 3
    400028: Particle View 001 4
    400030: Particle View 001 5
    400032: Particle View 001 6
    400034: Particle View 001 7
    400036: //RootNode
    400038: weixiujianzhu_01
    400040: Bone019
    400042: Bone020
    400044: Bone021
    400046: Bone022
    400048: weixiujianzhu_01 1
    400050: Point001
    4300000: weixiujianzhu_01
    4300002: weixiujianzhu_01
    7400000: Take 001
    9500000: //RootNode
    11100000: //RootNode
    13700000: weixiujianzhu_01
    13700002: weixiujianzhu_01 1
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 1
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
