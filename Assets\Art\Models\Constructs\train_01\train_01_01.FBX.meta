fileFormatVersion: 2
guid: 95bd350966d71724892fae62f26475a1
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Bone001
  - first:
      1: 100002
    second: Bone001(mirrored)
  - first:
      1: 100004
    second: Bone002
  - first:
      1: 100006
    second: Bone002(mirrored)
  - first:
      1: 100008
    second: Bone003
  - first:
      1: 100010
    second: Bone003(mirrored)
  - first:
      1: 100012
    second: Bone004
  - first:
      1: 100014
    second: Bone004(mirrored)
  - first:
      1: 100016
    second: Bone005
  - first:
      1: 100018
    second: Bone005(mirrored)
  - first:
      1: 100020
    second: Bone006
  - first:
      1: 100022
    second: Bone006(mirrored)
  - first:
      1: 100024
    second: Bone007
  - first:
      1: 100026
    second: Bone007(mirrored)
  - first:
      1: 100028
    second: Bone008
  - first:
      1: 100030
    second: Bone009
  - first:
      1: 100032
    second: Bone010
  - first:
      1: 100034
    second: Bone011
  - first:
      1: 100036
    second: Bone012
  - first:
      1: 100038
    second: Bone013
  - first:
      1: 100040
    second: Circle001
  - first:
      1: 100042
    second: <PERSON><PERSON> Chain001
  - first:
      1: 100044
    second: IK Chain002
  - first:
      1: 100046
    second: I<PERSON> Chain003
  - first:
      1: 100048
    second: IK Chain004
  - first:
      1: 100050
    second: IK Chain005
  - first:
      1: 100052
    second: IK Chain006
  - first:
      1: 100054
    second: Object002
  - first:
      1: 100056
    second: Point001
  - first:
      1: 100058
    second: train_01
  - first:
      1: 100060
    second: //RootNode
  - first:
      1: 100062
    second: Train_01_bone
  - first:
      1: 100064
    second: Point002
  - first:
      1: 100066
    second: Point003
  - first:
      1: 100068
    second: Point004
  - first:
      1: 100070
    second: Point005
  - first:
      1: 100072
    second: Point006
  - first:
      1: 100074
    second: Point007
  - first:
      1: 100076
    second: Point008
  - first:
      4: 400000
    second: Bone001
  - first:
      4: 400002
    second: Bone001(mirrored)
  - first:
      4: 400004
    second: Bone002
  - first:
      4: 400006
    second: Bone002(mirrored)
  - first:
      4: 400008
    second: Bone003
  - first:
      4: 400010
    second: Bone003(mirrored)
  - first:
      4: 400012
    second: Bone004
  - first:
      4: 400014
    second: Bone004(mirrored)
  - first:
      4: 400016
    second: Bone005
  - first:
      4: 400018
    second: Bone005(mirrored)
  - first:
      4: 400020
    second: Bone006
  - first:
      4: 400022
    second: Bone006(mirrored)
  - first:
      4: 400024
    second: Bone007
  - first:
      4: 400026
    second: Bone007(mirrored)
  - first:
      4: 400028
    second: Bone008
  - first:
      4: 400030
    second: Bone009
  - first:
      4: 400032
    second: Bone010
  - first:
      4: 400034
    second: Bone011
  - first:
      4: 400036
    second: Bone012
  - first:
      4: 400038
    second: Bone013
  - first:
      4: 400040
    second: Circle001
  - first:
      4: 400042
    second: IK Chain001
  - first:
      4: 400044
    second: IK Chain002
  - first:
      4: 400046
    second: IK Chain003
  - first:
      4: 400048
    second: IK Chain004
  - first:
      4: 400050
    second: IK Chain005
  - first:
      4: 400052
    second: IK Chain006
  - first:
      4: 400054
    second: Object002
  - first:
      4: 400056
    second: Point001
  - first:
      4: 400058
    second: train_01
  - first:
      4: 400060
    second: //RootNode
  - first:
      4: 400062
    second: Train_01_bone
  - first:
      4: 400064
    second: Point002
  - first:
      4: 400066
    second: Point003
  - first:
      4: 400068
    second: Point004
  - first:
      4: 400070
    second: Point005
  - first:
      4: 400072
    second: Point006
  - first:
      4: 400074
    second: Point007
  - first:
      4: 400076
    second: Point008
  - first:
      43: 4300000
    second: train_01
  - first:
      95: 9500000
    second: //RootNode
  - first:
      111: 11100000
    second: //RootNode
  - first:
      137: 13700000
    second: train_01
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - 861a6256f7e7493419f22dfecfded305
  - 25f67774593c8e7439ef286800fb2a1d
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 1
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
