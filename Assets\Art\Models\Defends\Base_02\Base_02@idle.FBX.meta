fileFormatVersion: 2
guid: b6ab2703b144fc546bd712f2584be6c7
timeCreated: **********
licenseType: Free
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Base_01
    100002: Base_01_plane
    100004: //RootNode
    100006: bone001
    100008: bone002
    100010: bone003
    100012: bone004
    100014: bone005
    100016: bone006
    100018: bone007
    100020: bone008
    100022: bone009
    100024: bone010
    100026: bone011
    100028: bone012
    100030: bone013
    100032: bone014
    100034: bone015
    100036: bone016
    100038: bone017
    100040: bone018
    100042: bone019
    100044: bone020
    100046: bone021
    100048: bone022
    100050: bone023
    100052: bone024
    100054: bone025
    100056: D_WP01
    100058: D_WP02
    100060: base_02
    100062: Bone001
    100064: bone026
    100066: bone027
    100068: bone028
    100070: bone029
    100072: bone030
    100074: bone031
    100076: bone032
    100078: bone033
    100080: Plane042
    400000: Base_01
    400002: Base_01_plane
    400004: //RootNode
    400006: bone001
    400008: bone002
    400010: bone003
    400012: bone004
    400014: bone005
    400016: bone006
    400018: bone007
    400020: bone008
    400022: bone009
    400024: bone010
    400026: bone011
    400028: bone012
    400030: bone013
    400032: bone014
    400034: bone015
    400036: bone016
    400038: bone017
    400040: bone018
    400042: bone019
    400044: bone020
    400046: bone021
    400048: bone022
    400050: bone023
    400052: bone024
    400054: bone025
    400056: D_WP01
    400058: D_WP02
    400060: base_02
    400062: Bone001
    400064: bone026
    400066: bone027
    400068: bone028
    400070: bone029
    400072: bone030
    400074: bone031
    400076: bone032
    400078: bone033
    400080: Plane042
    2300000: Base_01_plane
    2300002: Plane042
    3300000: Base_01_plane
    3300002: Plane042
    4300000: Base_01
    4300002: Base_01_plane
    4300004: base_02
    4300006: Plane042
    7400000: idle
    9500000: //RootNode
    11100000: //RootNode
    13700000: Base_01
    13700002: base_02
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: idle
      takeName: Take 001
      firstFrame: 0
      lastFrame: 120
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 1
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: base_02
        weight: 1
      - path: bone033
        weight: 1
      - path: bone033/bone001
        weight: 1
      - path: bone033/bone002
        weight: 1
      - path: bone033/bone003
        weight: 1
      - path: bone033/bone004
        weight: 1
      - path: bone033/bone005
        weight: 1
      - path: bone033/bone006
        weight: 1
      - path: bone033/bone007
        weight: 1
      - path: bone033/bone008
        weight: 1
      - path: bone033/bone009
        weight: 1
      - path: bone033/bone010
        weight: 1
      - path: bone033/bone011
        weight: 1
      - path: bone033/bone012
        weight: 1
      - path: bone033/bone013
        weight: 1
      - path: bone033/bone013/Bone001
        weight: 1
      - path: bone033/bone014
        weight: 1
      - path: bone033/bone015
        weight: 1
      - path: bone033/bone016
        weight: 1
      - path: bone033/bone017
        weight: 1
      - path: bone033/bone018
        weight: 1
      - path: bone033/bone019
        weight: 1
      - path: bone033/bone020
        weight: 1
      - path: bone033/bone021
        weight: 1
      - path: bone033/bone022
        weight: 1
      - path: bone033/bone023
        weight: 1
      - path: bone033/bone024
        weight: 1
      - path: bone033/bone025
        weight: 1
      - path: bone033/bone026
        weight: 1
      - path: bone033/bone027
        weight: 1
      - path: bone033/bone028
        weight: 1
      - path: bone033/bone029
        weight: 1
      - path: bone033/bone030
        weight: 1
      - path: bone033/bone031
        weight: 1
      - path: bone033/bone032
        weight: 1
      - path: bone033/Plane042
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 1
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
