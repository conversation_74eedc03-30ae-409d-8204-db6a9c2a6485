fileFormatVersion: 2
guid: 52a817650c248de4f92eee8c3d008467
timeCreated: **********
licenseType: Free
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: ball_01
    100002: Bip001
    100004: Bip001 Footsteps
    100006: Bip001 Head
    100008: Bip001 HeadNub
    100010: Bip001 L Calf
    100012: Bip001 L Clavicle
    100014: Bip001 L Foot
    100016: Bip001 L Forearm
    100018: Bip001 L Hand
    100020: Bip001 L Thigh
    100022: Bip001 L Toe0
    100024: Bip001 L Toe0Nub
    100026: Bip001 L UpperArm
    100028: Bip001 Neck
    100030: Bip001 Pelvis
    100032: Bip001 Prop1
    100034: Bip001 R Calf
    100036: Bip001 R Clavicle
    100038: Bip001 R Foot
    100040: Bip001 R Forearm
    100042: Bip001 R Hand
    100044: Bip001 R Thigh
    100046: Bip001 R Toe0
    100048: Bip001 R Toe0Nub
    100050: Bip001 R UpperArm
    100052: Bip001 Spine
    100054: Bip001 Spine1
    100056: Bone001
    100058: Bone002
    100060: currency_sniper_01
    100062: //RootNode
    100064: D_Chest
    100066: D_Head
    100068: 'D_L_Foot '
    100070: D_L_hand
    100072: 'D_R_Foot '
    100074: D_R_hand
    400000: ball_01
    400002: Bip001
    400004: Bip001 Footsteps
    400006: Bip001 Head
    400008: Bip001 HeadNub
    400010: Bip001 L Calf
    400012: Bip001 L Clavicle
    400014: Bip001 L Foot
    400016: Bip001 L Forearm
    400018: Bip001 L Hand
    400020: Bip001 L Thigh
    400022: Bip001 L Toe0
    400024: Bip001 L Toe0Nub
    400026: Bip001 L UpperArm
    400028: Bip001 Neck
    400030: Bip001 Pelvis
    400032: Bip001 Prop1
    400034: Bip001 R Calf
    400036: Bip001 R Clavicle
    400038: Bip001 R Foot
    400040: Bip001 R Forearm
    400042: Bip001 R Hand
    400044: Bip001 R Thigh
    400046: Bip001 R Toe0
    400048: Bip001 R Toe0Nub
    400050: Bip001 R UpperArm
    400052: Bip001 Spine
    400054: Bip001 Spine1
    400056: Bone001
    400058: Bone002
    400060: currency_sniper_01
    400062: //RootNode
    400064: D_Chest
    400066: D_Head
    400068: 'D_L_Foot '
    400070: D_L_hand
    400072: 'D_R_Foot '
    400074: D_R_hand
    4300000: currency_sniper_01
    4300002: ball_01
    7400000: run
    9500000: //RootNode
    11100000: //RootNode
    13700000: ball_01
    13700002: currency_sniper_01
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: run
      takeName: Take 001
      firstFrame: 0
      lastFrame: 24
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 1
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: ball_01
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bip001 Footsteps
        weight: 1
      - path: Bip001/Bip001 Pelvis
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh/Bip001 L Calf
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh/Bip001 L Calf/Bip001
          L Foot
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh/Bip001 L Calf/Bip001
          L Foot/Bip001 L Toe0
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh/Bip001 L Calf/Bip001
          L Foot/Bip001 L Toe0/Bip001 L Toe0Nub
        weight: 1
      - path: 'Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh/Bip001 L Calf/Bip001
          L Foot/D_L_Foot '
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh/Bip001 R Calf
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh/Bip001 R Calf/Bip001
          R Foot
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh/Bip001 R Calf/Bip001
          R Foot/Bip001 R Toe0
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh/Bip001 R Calf/Bip001
          R Foot/Bip001 R Toe0/Bip001 R Toe0Nub
        weight: 1
      - path: 'Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh/Bip001 R Calf/Bip001
          R Foot/D_R_Foot '
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 Head
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 Head/Bip001
          HeadNub
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 Head/Bone001
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 Head/D_Head
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 L
          Clavicle
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 L
          Clavicle/Bip001 L UpperArm
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 L
          Clavicle/Bip001 L UpperArm/Bip001 L Forearm
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 L
          Clavicle/Bip001 L UpperArm/Bip001 L Forearm/Bip001 L Hand
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 L
          Clavicle/Bip001 L UpperArm/Bip001 L Forearm/Bip001 L Hand/D_L_hand
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 R
          Clavicle
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 R
          Clavicle/Bip001 R UpperArm
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 R
          Clavicle/Bip001 R UpperArm/Bip001 R Forearm
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 R
          Clavicle/Bip001 R UpperArm/Bip001 R Forearm/Bip001 R Hand
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001 R
          Clavicle/Bip001 R UpperArm/Bip001 R Forearm/Bip001 R Hand/D_R_hand
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/D_Chest
        weight: 1
      - path: Bip001/Bip001 Prop1
        weight: 1
      - path: Bip001/Bip001 Prop1/Bone002
        weight: 1
      - path: currency_sniper_01
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 1
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
