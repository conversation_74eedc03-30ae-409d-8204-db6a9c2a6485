fileFormatVersion: 2
guid: 5e1559562688d444a9c9eb45bb44d34b
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Bone001
  - first:
      1: 100002
    second: Bone002
  - first:
      1: 100004
    second: Bone003
  - first:
      1: 100006
    second: Bone004
  - first:
      1: 100008
    second: Bone005
  - first:
      1: 100010
    second: IK Chain001
  - first:
      1: 100012
    second: Particle View 001
  - first:
      1: 100014
    second: //RootNode
  - first:
      1: 100016
    second: youtian_04
  - first:
      1: 100018
    second: youtian_01
  - first:
      1: 100020
    second: youtian_02
  - first:
      1: 100022
    second: youtian_03
  - first:
      4: 400000
    second: Bone001
  - first:
      4: 400002
    second: Bone002
  - first:
      4: 400004
    second: Bone003
  - first:
      4: 400006
    second: Bone004
  - first:
      4: 400008
    second: Bone005
  - first:
      4: 400010
    second: IK Chain001
  - first:
      4: 400012
    second: Particle View 001
  - first:
      4: 400014
    second: //RootNode
  - first:
      4: 400016
    second: youtian_04
  - first:
      4: 400018
    second: youtian_01
  - first:
      4: 400020
    second: youtian_02
  - first:
      4: 400022
    second: youtian_03
  - first:
      23: 2300000
    second: youtian_01
  - first:
      23: 2300002
    second: youtian_02
  - first:
      23: 2300004
    second: youtian_03
  - first:
      33: 3300000
    second: youtian_01
  - first:
      33: 3300002
    second: youtian_02
  - first:
      33: 3300004
    second: youtian_03
  - first:
      43: 4300000
    second: youtian_04
  - first:
      43: 4300002
    second: youtian_01
  - first:
      43: 4300004
    second: youtian_02
  - first:
      43: 4300006
    second: youtian_03
  - first:
      95: 9500000
    second: //RootNode
  - first:
      111: 11100000
    second: //RootNode
  - first:
      137: 13700000
    second: youtian_04
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - c0775a48f58a3d8489a85ac5e7fefd95
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 1
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
