fileFormatVersion: 2
guid: edc871f83cccbaa459945b2f33c38098
timeCreated: **********
licenseType: Free
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Bone01
    100002: Bone02
    100004: Bone03
    100006: Bone04
    100008: Bone05
    100010: Bone06
    100012: Box_001
    100014: //RootNode
    100016: Bone006
    100018: Bone007
    100020: Bone008
    100022: Bone009
    100024: Bone009(mirrored)
    100026: Bone010
    100028: Bone010(mirrored)
    100030: Bone011
    100032: Bone011(mirrored)
    100034: zhihuizhongxin_01
    100036: Bone012
    100038: Bone013
    100040: Bone014
    100042: Bone015
    100044: Bone016
    100046: Bone017
    400000: Bone01
    400002: Bone02
    400004: Bone03
    400006: Bone04
    400008: Bone05
    400010: Bone06
    400012: Box_001
    400014: //RootNode
    400016: Bone006
    400018: Bone007
    400020: Bone008
    400022: Bone009
    400024: Bone009(mirrored)
    400026: Bone010
    400028: Bone010(mirrored)
    400030: Bone011
    400032: Bone011(mirrored)
    400034: zhi<PERSON>zhongxin_01
    400036: Bone012
    400038: Bone013
    400040: Bone014
    400042: Bone015
    400044: Bone016
    400046: Bone017
    4300000: Box_001
    4300002: zhihuizhongxin_01
    7400000: idle
    9500000: //RootNode
    11100000: //RootNode
    13700000: Box_001
    13700002: zhihuizhongxin_01
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: idle
      takeName: Take 001
      firstFrame: 0
      lastFrame: 300
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bone01
        weight: 1
      - path: Bone01/Bone02
        weight: 1
      - path: Bone01/Bone03
        weight: 1
      - path: Bone01/Bone04
        weight: 1
      - path: Bone01/Bone05
        weight: 1
      - path: Bone01/Bone06
        weight: 1
      - path: Box_001
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 1
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
