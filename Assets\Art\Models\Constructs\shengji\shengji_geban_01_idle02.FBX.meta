fileFormatVersion: 2
guid: 9c7d6ce74d55b2f42b24200a981cc800
timeCreated: **********
licenseType: Free
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Bone001
    100002: Bone002
    100004: <PERSON>003
    100006: <PERSON>00<PERSON>
    100008: <PERSON>00<PERSON>
    100010: Bone011
    100012: Bone012
    100014: Bone013
    100016: <PERSON>014
    100018: Bone015
    100020: Bone016
    100022: Bone017
    100024: Bone018
    100026: Camera01
    100028: Camera01 1
    100030: Camera01.Target
    100032: Camera01.Target 1
    100034: Dummy001
    100036: Object024
    100038: Particle View 001
    100040: Particle View 001 1
    100042: Particle View 001 2
    100044: Particle View 001 3
    100046: Particle View 001 4
    100048: Particle View 001 5
    100050: Particle View 001 6
    100052: Particle View 001 7
    100054: //RootNode
    100056: weixiujianzhu_01
    400000: Bone001
    400002: Bone002
    400004: Bone003
    400006: <PERSON>004
    400008: <PERSON><PERSON><PERSON>
    400010: <PERSON>011
    400012: <PERSON>012
    400014: <PERSON>01<PERSON>
    400016: <PERSON><PERSON><PERSON>
    400018: <PERSON>015
    400020: Bone016
    400022: Bone017
    400024: Bone018
    400026: <PERSON><PERSON>
    400028: Camera01 1
    400030: Camera01.Target
    400032: Camera01.Target 1
    400034: Dummy001
    400036: Object024
    400038: Particle View 001
    400040: Particle View 001 1
    400042: Particle View 001 2
    400044: Particle View 001 3
    400046: Particle View 001 4
    400048: Particle View 001 5
    400050: Particle View 001 6
    400052: Particle View 001 7
    400054: //RootNode
    400056: weixiujianzhu_01
    4300000: weixiujianzhu_01
    7400000: Take 001
    9500000: //RootNode
    11100000: //RootNode
    13700000: weixiujianzhu_01
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 1
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
