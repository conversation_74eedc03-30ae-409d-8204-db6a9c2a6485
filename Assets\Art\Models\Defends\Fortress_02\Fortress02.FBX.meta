fileFormatVersion: 2
guid: ab4fd19ca0dc50149a4768917865e318
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: bone001
  - first:
      1: 100002
    second: bone002
  - first:
      1: 100004
    second: bone003
  - first:
      1: 100006
    second: bone004
  - first:
      1: 100008
    second: bone005
  - first:
      1: 100010
    second: bone006
  - first:
      1: 100012
    second: bone007
  - first:
      1: 100014
    second: bone008
  - first:
      1: 100016
    second: bone009
  - first:
      1: 100018
    second: bone010
  - first:
      1: 100020
    second: bone011
  - first:
      1: 100022
    second: bone012
  - first:
      1: 100024
    second: bone013
  - first:
      1: 100026
    second: bone014
  - first:
      1: 100028
    second: bone015
  - first:
      1: 100030
    second: bone016
  - first:
      1: 100032
    second: bone017
  - first:
      1: 100034
    second: bone018
  - first:
      1: 100036
    second: bone019
  - first:
      1: 100038
    second: bone020
  - first:
      1: 100040
    second: bone021
  - first:
      1: 100042
    second: bone022
  - first:
      1: 100044
    second: bone023
  - first:
      1: 100046
    second: bone024
  - first:
      1: 100048
    second: bone025
  - first:
      1: 100050
    second: bone026
  - first:
      1: 100052
    second: bone027
  - first:
      1: 100054
    second: //RootNode
  - first:
      1: 100056
    second: Fortress_02
  - first:
      1: 100058
    second: Fortress_02_Plane
  - first:
      1: 100060
    second: D_WP_01
  - first:
      1: 100062
    second: D_WP_02
  - first:
      1: 100064
    second: bone028
  - first:
      1: 100066
    second: bone029
  - first:
      1: 100068
    second: bone030
  - first:
      1: 100070
    second: bone031
  - first:
      1: 100072
    second: D_WP
  - first:
      1: 100074
    second: Fortress02
  - first:
      1: 100076
    second: Fortress02_plane
  - first:
      4: 400000
    second: bone001
  - first:
      4: 400002
    second: bone002
  - first:
      4: 400004
    second: bone003
  - first:
      4: 400006
    second: bone004
  - first:
      4: 400008
    second: bone005
  - first:
      4: 400010
    second: bone006
  - first:
      4: 400012
    second: bone007
  - first:
      4: 400014
    second: bone008
  - first:
      4: 400016
    second: bone009
  - first:
      4: 400018
    second: bone010
  - first:
      4: 400020
    second: bone011
  - first:
      4: 400022
    second: bone012
  - first:
      4: 400024
    second: bone013
  - first:
      4: 400026
    second: bone014
  - first:
      4: 400028
    second: bone015
  - first:
      4: 400030
    second: bone016
  - first:
      4: 400032
    second: bone017
  - first:
      4: 400034
    second: bone018
  - first:
      4: 400036
    second: bone019
  - first:
      4: 400038
    second: bone020
  - first:
      4: 400040
    second: bone021
  - first:
      4: 400042
    second: bone022
  - first:
      4: 400044
    second: bone023
  - first:
      4: 400046
    second: bone024
  - first:
      4: 400048
    second: bone025
  - first:
      4: 400050
    second: bone026
  - first:
      4: 400052
    second: bone027
  - first:
      4: 400054
    second: //RootNode
  - first:
      4: 400056
    second: Fortress_02
  - first:
      4: 400058
    second: Fortress_02_Plane
  - first:
      4: 400060
    second: D_WP_01
  - first:
      4: 400062
    second: D_WP_02
  - first:
      4: 400064
    second: bone028
  - first:
      4: 400066
    second: bone029
  - first:
      4: 400068
    second: bone030
  - first:
      4: 400070
    second: bone031
  - first:
      4: 400072
    second: D_WP
  - first:
      4: 400074
    second: Fortress02
  - first:
      4: 400076
    second: Fortress02_plane
  - first:
      23: 2300000
    second: Fortress_02_Plane
  - first:
      23: 2300002
    second: Fortress02_plane
  - first:
      33: 3300000
    second: Fortress_02_Plane
  - first:
      33: 3300002
    second: Fortress02_plane
  - first:
      43: 4300000
    second: Fortress_02_Plane
  - first:
      43: 4300002
    second: Fortress_02
  - first:
      43: 4300004
    second: Fortress02
  - first:
      43: 4300006
    second: Fortress02_plane
  - first:
      95: 9500000
    second: //RootNode
  - first:
      111: 11100000
    second: //RootNode
  - first:
      137: 13700000
    second: Fortress_02
  - first:
      137: 13700002
    second: Fortress02
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - dba1bb797f06977408f631adea0b870e
  - b399dc738574c3b439a0bb31bd2b8400
  - b6ad9bff82c4ec749b2cde507fbaacc0
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 1
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
