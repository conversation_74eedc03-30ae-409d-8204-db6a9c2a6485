fileFormatVersion: 2
guid: bf4269e450e2ce34f9ff48bda78a88ea
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: bone001
  - first:
      1: 100002
    second: bone002
  - first:
      1: 100004
    second: bone003
  - first:
      1: 100006
    second: bone004
  - first:
      1: 100008
    second: bone005
  - first:
      1: 100010
    second: bone006
  - first:
      1: 100012
    second: bone007
  - first:
      1: 100014
    second: bone008
  - first:
      1: 100016
    second: bone009
  - first:
      1: 100018
    second: Plane030
  - first:
      1: 100020
    second: //RootNode
  - first:
      1: 100022
    second: sandbag_01
  - first:
      1: 100024
    second: Object001
  - first:
      1: 100026
    second: Object002
  - first:
      1: 100028
    second: Object003
  - first:
      1: 100030
    second: Object004
  - first:
      1: 100032
    second: Object005
  - first:
      1: 100034
    second: Object006
  - first:
      1: 100036
    second: Object007
  - first:
      1: 100038
    second: Object008
  - first:
      1: 100040
    second: Bone011
  - first:
      4: 400000
    second: bone001
  - first:
      4: 400002
    second: bone002
  - first:
      4: 400004
    second: bone003
  - first:
      4: 400006
    second: bone004
  - first:
      4: 400008
    second: bone005
  - first:
      4: 400010
    second: bone006
  - first:
      4: 400012
    second: bone007
  - first:
      4: 400014
    second: bone008
  - first:
      4: 400016
    second: bone009
  - first:
      4: 400018
    second: Plane030
  - first:
      4: 400020
    second: //RootNode
  - first:
      4: 400022
    second: sandbag_01
  - first:
      4: 400024
    second: Object001
  - first:
      4: 400026
    second: Object002
  - first:
      4: 400028
    second: Object003
  - first:
      4: 400030
    second: Object004
  - first:
      4: 400032
    second: Object005
  - first:
      4: 400034
    second: Object006
  - first:
      4: 400036
    second: Object007
  - first:
      4: 400038
    second: Object008
  - first:
      4: 400040
    second: Bone011
  - first:
      23: 2300000
    second: Plane030
  - first:
      33: 3300000
    second: Plane030
  - first:
      43: 4300000
    second: sandbag_01
  - first:
      43: 4300002
    second: Plane030
  - first:
      43: 4300004
    second: Object001
  - first:
      43: 4300006
    second: Object002
  - first:
      43: 4300008
    second: Object003
  - first:
      43: 4300010
    second: Object004
  - first:
      43: 4300012
    second: Object005
  - first:
      43: 4300014
    second: Object006
  - first:
      43: 4300016
    second: Object007
  - first:
      43: 4300018
    second: Object008
  - first:
      95: 9500000
    second: //RootNode
  - first:
      111: 11100000
    second: //RootNode
  - first:
      137: 13700000
    second: sandbag_01
  - first:
      137: 13700002
    second: Object001
  - first:
      137: 13700004
    second: Object002
  - first:
      137: 13700006
    second: Object003
  - first:
      137: 13700008
    second: Object004
  - first:
      137: 13700010
    second: Object005
  - first:
      137: 13700012
    second: Object006
  - first:
      137: 13700014
    second: Object007
  - first:
      137: 13700016
    second: Object008
  - first:
      137: 13700018
    second: Plane030
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - 1721ce4667678f74cb5078322df3eee5
  - 3548d64f0b60f874fb5bd2b65aacc5bc
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 1
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
