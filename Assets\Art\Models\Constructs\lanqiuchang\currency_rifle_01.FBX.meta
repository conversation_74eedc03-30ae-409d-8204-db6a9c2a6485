fileFormatVersion: 2
guid: 83f708b33612948419b9d26038a5e43a
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: ball_01
  - first:
      1: 100002
    second: Bip001
  - first:
      1: 100004
    second: Bip001 Footsteps
  - first:
      1: 100006
    second: Bip001 Head
  - first:
      1: 100008
    second: Bip001 HeadNub
  - first:
      1: 100010
    second: Bip001 L Calf
  - first:
      1: 100012
    second: Bip001 L Clavicle
  - first:
      1: 100014
    second: Bip001 L Foot
  - first:
      1: 100016
    second: Bip001 L Forearm
  - first:
      1: 100018
    second: Bip001 L Hand
  - first:
      1: 100020
    second: Bip001 L Thigh
  - first:
      1: 100022
    second: Bip001 L Toe0
  - first:
      1: 100024
    second: Bip001 L Toe0Nub
  - first:
      1: 100026
    second: Bip001 L UpperArm
  - first:
      1: 100028
    second: Bip001 Neck
  - first:
      1: 100030
    second: Bip001 Pelvis
  - first:
      1: 100032
    second: Bip001 Prop1
  - first:
      1: 100034
    second: Bip001 R Calf
  - first:
      1: 100036
    second: Bip001 R Clavicle
  - first:
      1: 100038
    second: Bip001 R Foot
  - first:
      1: 100040
    second: Bip001 R Forearm
  - first:
      1: 100042
    second: Bip001 R Hand
  - first:
      1: 100044
    second: Bip001 R Thigh
  - first:
      1: 100046
    second: Bip001 R Toe0
  - first:
      1: 100048
    second: Bip001 R Toe0Nub
  - first:
      1: 100050
    second: Bip001 R UpperArm
  - first:
      1: 100052
    second: Bip001 Spine
  - first:
      1: 100054
    second: Bip001 Spine1
  - first:
      1: 100056
    second: Bone001
  - first:
      1: 100058
    second: Bone002
  - first:
      1: 100060
    second: currency_rifle_01
  - first:
      1: 100062
    second: //RootNode
  - first:
      1: 100064
    second: D_Chest
  - first:
      1: 100066
    second: D_Head
  - first:
      1: 100068
    second: 'D_L_Foot '
  - first:
      1: 100070
    second: D_L_hand
  - first:
      1: 100072
    second: 'D_R_Foot '
  - first:
      1: 100074
    second: D_R_hand
  - first:
      4: 400000
    second: ball_01
  - first:
      4: 400002
    second: Bip001
  - first:
      4: 400004
    second: Bip001 Footsteps
  - first:
      4: 400006
    second: Bip001 Head
  - first:
      4: 400008
    second: Bip001 HeadNub
  - first:
      4: 400010
    second: Bip001 L Calf
  - first:
      4: 400012
    second: Bip001 L Clavicle
  - first:
      4: 400014
    second: Bip001 L Foot
  - first:
      4: 400016
    second: Bip001 L Forearm
  - first:
      4: 400018
    second: Bip001 L Hand
  - first:
      4: 400020
    second: Bip001 L Thigh
  - first:
      4: 400022
    second: Bip001 L Toe0
  - first:
      4: 400024
    second: Bip001 L Toe0Nub
  - first:
      4: 400026
    second: Bip001 L UpperArm
  - first:
      4: 400028
    second: Bip001 Neck
  - first:
      4: 400030
    second: Bip001 Pelvis
  - first:
      4: 400032
    second: Bip001 Prop1
  - first:
      4: 400034
    second: Bip001 R Calf
  - first:
      4: 400036
    second: Bip001 R Clavicle
  - first:
      4: 400038
    second: Bip001 R Foot
  - first:
      4: 400040
    second: Bip001 R Forearm
  - first:
      4: 400042
    second: Bip001 R Hand
  - first:
      4: 400044
    second: Bip001 R Thigh
  - first:
      4: 400046
    second: Bip001 R Toe0
  - first:
      4: 400048
    second: Bip001 R Toe0Nub
  - first:
      4: 400050
    second: Bip001 R UpperArm
  - first:
      4: 400052
    second: Bip001 Spine
  - first:
      4: 400054
    second: Bip001 Spine1
  - first:
      4: 400056
    second: Bone001
  - first:
      4: 400058
    second: Bone002
  - first:
      4: 400060
    second: //RootNode
  - first:
      4: 400062
    second: currency_rifle_01
  - first:
      4: 400064
    second: D_Chest
  - first:
      4: 400066
    second: D_Head
  - first:
      4: 400068
    second: 'D_L_Foot '
  - first:
      4: 400070
    second: D_L_hand
  - first:
      4: 400072
    second: 'D_R_Foot '
  - first:
      4: 400074
    second: D_R_hand
  - first:
      43: 4300000
    second: currency_rifle_01
  - first:
      43: 4300002
    second: ball_01
  - first:
      74: 7400000
    second: Take 001
  - first:
      95: 9500000
    second: //RootNode
  - first:
      111: 11100000
    second: //RootNode
  - first:
      137: 13700000
    second: ball_01
  - first:
      137: 13700002
    second: currency_rifle_01
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Take 001
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 100
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 1
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: ball_01
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bip001 Footsteps
        weight: 1
      - path: Bip001/Bip001 Pelvis
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh/Bip001 L Calf
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh/Bip001 L Calf/Bip001
          L Foot
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh/Bip001 L Calf/Bip001
          L Foot/Bip001 L Toe0
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh/Bip001 L Calf/Bip001
          L Foot/Bip001 L Toe0/Bip001 L Toe0Nub
        weight: 1
      - path: 'Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh/Bip001 L Calf/Bip001
          L Foot/D_L_Foot '
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh/Bip001 R Calf
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh/Bip001 R Calf/Bip001
          R Foot
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh/Bip001 R Calf/Bip001
          R Foot/Bip001 R Toe0
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh/Bip001 R Calf/Bip001
          R Foot/Bip001 R Toe0/Bip001 R Toe0Nub
        weight: 1
      - path: 'Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh/Bip001 R Calf/Bip001
          R Foot/D_R_Foot '
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          Head
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          Head/Bip001 HeadNub
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          Head/Bone001
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          Head/D_Head
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          L Clavicle
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          L Clavicle/Bip001 L UpperArm
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          L Clavicle/Bip001 L UpperArm/Bip001 L Forearm
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          L Clavicle/Bip001 L UpperArm/Bip001 L Forearm/Bip001 L Hand
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          L Clavicle/Bip001 L UpperArm/Bip001 L Forearm/Bip001 L Hand/D_L_hand
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          R Clavicle
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          R Clavicle/Bip001 R UpperArm
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          R Clavicle/Bip001 R UpperArm/Bip001 R Forearm
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          R Clavicle/Bip001 R UpperArm/Bip001 R Forearm/Bip001 R Hand
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/Bip001 Neck/Bip001
          R Clavicle/Bip001 R UpperArm/Bip001 R Forearm/Bip001 R Hand/D_R_hand
        weight: 1
      - path: Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/D_Chest
        weight: 1
      - path: Bip001/Bip001 Prop1
        weight: 1
      - path: Bip001/Bip001 Prop1/Bone002
        weight: 1
      - path: currency_rifle_01
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - 005a944724f5d7e469cddf7dd7c43c5a
  - ad6ef307048418a4fbd8f9e933552ed5
  - eccf895c3f64a5446ba1dcbc10f23484
  - 609f2a509d19da0459d2060c8ff8c8e7
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 1
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
